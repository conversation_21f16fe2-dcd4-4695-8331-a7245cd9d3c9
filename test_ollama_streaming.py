#!/usr/bin/env python3
"""
Test script to verify Ollama streaming functionality
"""
import requests
import json
import time

def test_ollama_streaming():
    """Test streaming response from Ollama"""
    print("🧪 Testing Ollama streaming...")
    
    url = "http://localhost:11434/api/generate"
    payload = {
        "model": "gemma3n:latest",
        "prompt": "Write a short poem about AI assistants.",
        "stream": True
    }
    
    try:
        print(f"📤 Sending request to {url}")
        response = requests.post(url, json=payload, stream=True, timeout=60)
        
        if response.status_code != 200:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
        print("✅ Connected, processing stream...")
        print("📝 Streaming response:")
        print("-" * 50)
        
        full_response = ""
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                try:
                    chunk_data = json.loads(line.decode('utf-8'))
                    chunk_text = chunk_data.get('response', '')
                    is_done = chunk_data.get('done', False)
                    
                    if chunk_text:
                        print(chunk_text, end='', flush=True)
                        full_response += chunk_text
                        chunk_count += 1
                    
                    if is_done:
                        print(f"\n\n✅ Streaming completed!")
                        print(f"📊 Total chunks: {chunk_count}")
                        print(f"📏 Total length: {len(full_response)} characters")
                        return True
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON decode error: {e}")
                    continue
                    
        print("\n❌ Stream ended without completion signal")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out after 60 seconds")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama. Is it running?")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_ollama_health():
    """Test if Ollama is healthy and models are available"""
    print("🏥 Testing Ollama health...")
    
    try:
        # Test service health
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print("✅ Ollama service is healthy")
            print(f"📋 Available models: {len(models.get('models', []))}")
            
            # Check if gemma3n is available
            model_names = [model['name'] for model in models.get('models', [])]
            if 'gemma3n:latest' in model_names:
                print("✅ Gemma 3n model is available")
                return True
            else:
                print("❌ Gemma 3n model not found")
                print(f"Available models: {model_names}")
                return False
        else:
            print(f"❌ Ollama health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Ollama tests...\n")
    
    # Test health first
    if test_ollama_health():
        print("\n" + "="*60 + "\n")
        # Test streaming
        test_ollama_streaming()
    else:
        print("\n❌ Health check failed, skipping streaming test")
    
    print("\n🏁 Tests completed")
